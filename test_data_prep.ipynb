#%%
import os
import time

import torch
from sgfmill import sgf, sgf_moves

BOARD_SIZE = 19
device = "cuda"
#%%
def data_point(board, move, color):
    board_array = torch.zeros((1, BOARD_SIZE, BOARD_SIZE), dtype=torch.float32, device=device)
    for p in board.list_occupied_points():
        board_array[0, p[1][0], p[1][1]] = -1.0 + 2 * int(p[0] == color)
    return board_array, move[0]*BOARD_SIZE+move[1]

def make_data_points(game_files):
    data_points = []
    for i, game_file in enumerate(game_files):
        # print('Processing %s/%s: %s' % (i, len(game_files), game_file))
        with open(game_file) as f:
            contents = f.read().encode('ascii')
            game = sgf.Sgf_game.from_bytes(contents)
            board, plays = sgf_moves.get_setup_and_moves(game)
            for color, move in plays:
                if move is None: continue
                row, col = move
                tp = data_point(board, move, color)
                data_points.append(tp)
                board.play(row, col, color)
    return data_points
#%%
data_points = make_data_points(["data/kgs_data/unzipped/KGS-2001-19-2298-/KGS2001/2000-7-19-1.sgf"])
#%%
import numpy as np
from pysgf import SGF

from old.board.bfs_board import BFSGoBoard


def load_game_into_numpy_array(root):
    """
    Loads a game into a numpy array.
    :param root: The root SGFNode of the game.
    :return: A tuple of the boards, moves, and winner.
    The boards are a list of numpy arrays representing the board state at each move.
    The moves are a list of tuples representing the move coordinates and the player.
    The winner is a string representing the winner of the game.
    """
    cur = root

    initial_board = BFSGoBoard.create_empty_board(root.board_size)
    if root.move_with_placements:
        for move in root.move_with_placements:
            initial_board = initial_board.make_move((move.coords[::-1], 1 if move.player == "B" else -1))

    boards = [initial_board]
    moves = []

    while cur.children:
        cur = cur.children[0]
        move = cur.move
        if move is None or move.coords is None:
            break
        player = 1 if move.player == "B" else -1
        coords = move.coords[::-1]
        moves.append((coords, player))
        boards.append(boards[-1].make_move((coords, player)))
    result = None
    if "RE" in root.properties:
        result = 1 if "B" in root.properties["RE"][0] else -1
    return [board.board for board in boards], moves, result


def generate_data_from_file(sgf_file_path):
    """
    Creates training data from a sgf file.
    :param sgf_file_path: The path to the sgf file.
    :return: A tuple of the input features and the policy target.
    The input features are a numpy array of shape (num_moves, 19, 19) where each value is uint8 representing the 2 player's last 4 moves.
    The policy target is a numpy array of shape (num_moves, 361).
    """
    root = SGF.parse_file(sgf_file_path)
    if "SZ" in root.properties and root.properties["SZ"][0] != "19":
        print("Skipping non-19x19 game.")
        return [], []

    boards, moves, winner = load_game_into_numpy_array(root)

    input_features = []
    policy_targets = []
    for i in range(len(boards) - 1):
        input_features.append(boards[i]*moves[i][1])
        # input_features.append(generate_feature_map_packed(boards[:i + 1], moves[i][1]))
        policy_targets.append(moves[i][0][0]*19+moves[i][0][1]) # Coords to flat_index
    return input_features, np.array(policy_targets, dtype=np.long)
#%%
files = []
for r, d, f in os.walk("/home/<USER>/PycharmProjects/GoExperiments/data/kgs_data/unzipped"):
    for file in f:
        if '.sgf' in file:
            files.append(os.path.join(r, file))
for file_i, file in enumerate(files):
    data_points = make_data_points([file])
    data_points_2 = generate_data_from_file(file)
    # assert len(data_points) == len(data_points_2[0])
    if len(data_points) != len(data_points_2[0]):
        print(f"{file} Problem Detected Length: {len(data_points)} != {len(data_points_2[0])}")
    for i in range(min(len(data_points),len(data_points_2[0]))):
        # assert np.array_equal(data_points[i][0].squeeze().cpu().numpy(), data_points_2[0][i])
        # assert data_points[i][1] == data_points_2[1][i]
        if not np.array_equal(data_points[i][0].squeeze().cpu().numpy(), data_points_2[0][i]):
            print(f"{file} Problem Detected Features")
        if data_points[i][1] != data_points_2[1][i]:
            print(f"{file} Problem Detected Policy: {data_points[i][1]} != {data_points_2[1][i]}")
    if file_i % 100 == 0:
        print(f"Processed {file_i} of {len(files)}")
#%%
file="/home/<USER>/PycharmProjects/GoExperiments/data/kgs_data/unzipped/KGS-2001-19-2298-/KGS2001/2001-01-04-1.sgf"
data_points = make_data_points([file])
data_points_2 = generate_data_from_file(file)
#%%
len(data_points), len(data_points_2)
#%%
path = '/home/<USER>/PycharmProjects/GoExperiments/data/kgs_data/unzipped'

game_files = []
# r=root, d=directories, f = files
for r, d, f in os.walk(path):
    for file in f:
        if '.sgf' in file:
            game_files.append(os.path.join(r, file))

print('Total games: %s'  % len(game_files))

training_game_files = game_files[:3000]
test_game_files = game_files[-100:]

from old.utilities.prepare_data import create_dataset

training_dataset = create_dataset(training_game_files)
test_dataset = create_dataset(test_game_files)
train_loader = torch.utils.data.DataLoader(training_dataset, batch_size=64, shuffle=True)
test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=1000, shuffle=True)
#%%
import time

for i in range(10):
    torch.cuda.synchronize()
    start = time.perf_counter()
    for i, data in enumerate(train_loader):
        (data[0].shape, data[1].shape)
    torch.cuda.synchronize()
    end = time.perf_counter()
    print(f"Time: {end - start}")

    torch.cuda.synchronize()
    start = time.perf_counter()
    for i, data in enumerate(test_loader):
        (data[0].shape, data[1].shape)
    torch.cuda.synchronize()
    end = time.perf_counter()
    print(f"Time: {end - start}")

    torch.cuda.synchronize()
    start = time.perf_counter()
    indices = torch.randperm(len(training_dataset))
    for i in range(0, len(training_dataset), 64):
        e = training_dataset.encoded_boards[indices[i:i+64]]
        p = training_dataset.policies[indices[i:i+64]]
        (e.shape, p.shape)
    torch.cuda.synchronize()
    end = time.perf_counter()
    print(f"Time: {end - start}")

    torch.cuda.synchronize()
    start = time.perf_counter()
    indices = torch.randperm(len(test_dataset))
    for i in range(0, len(test_dataset), 1000):
        e = test_dataset.encoded_boards[indices[i:i+1000]]
        p = test_dataset.policies[indices[i:i+1000]]
        (e.shape, p.shape)
    torch.cuda.synchronize()
    end = time.perf_counter()
    print(f"Time: {end - start}")
    print()
#%%
