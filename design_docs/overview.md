## Tromp-Taylor rules

## Boards:
- BoardState [19x19 int array]
- Make move
- Score
- Legal moves: any space that is empty. Track incrementally.
- Hash

## GameState:
- Set of visited boards
- Make move
- Current Board
- Hash

## Node:
- Make action (moves + pass)
- Observe
- Hash

Graph search will go over possible game states.

Self play will involve two MCGS players.
- Record action values

Self play will happen continuously.
- Occasionally self play will pull new models from the training process.
- These games will be saved to disk. About 14KB per 19x19 game.

A different process will constantly train on the last however many games.
- Model weights will periodically be saved to disk.
- Checkpoints are cut when loss change stabilizes.

Evaluator process will play tournaments between all the models stored by the training process.



Future:
- Score head