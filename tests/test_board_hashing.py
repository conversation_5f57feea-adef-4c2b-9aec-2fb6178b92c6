import unittest
import numpy as np
import functools  # For clearing lru_cache in tests

# Assuming your hash_utilities.py is in src.games.go
# Adjust the import path according to your project structure
from src.games.go.hash_utilities import get_zobrist_table, calculate_board_hash


class TestZobristHashing(unittest.TestCase):

    def setUp(self):
        """
        This method is called before each test.
        We can clear the lru_cache for get_zobrist_table to ensure
        tests are independent regarding table generation.
        """
        get_zobrist_table.cache_clear()

    def test_get_zobrist_table_properties(self):
        """
        Tests the properties of the generated Zobrist table.
        """
        shape = (3, 3)
        num_states = 3  # e.g., 0: empty, 1: black, 2: white

        table = get_zobrist_table(shape, num_states)

        # Test shape
        self.assertEqual(table.shape, (shape[0], shape[1], num_states))

        # Test dtype
        self.assertEqual(table.dtype, np.uint64)

        # Test that state 0 (empty) has hash 0
        self.assertTrue(np.all(table[:, :, 0] == 0))

        # Test that other states have non-zero hashes (for num_states > 1)
        if num_states > 1:
            self.assertTrue(np.all(table[:, :, 1:] != 0))

    def test_zobrist_table_memoization(self):
        """
        Tests that get_zobrist_table is memoized using lru_cache.
        """
        shape = (4, 4)
        num_states = 2

        table1 = get_zobrist_table(shape, num_states)
        table2 = get_zobrist_table(shape, num_states)
        self.assertIs(table1, table2, "Memoized function should return the same object for the same arguments.")

        # Test with different arguments
        table3 = get_zobrist_table((5, 5), num_states)
        self.assertIsNot(table1, table3, "Different arguments should result in a different table object.")

        table4 = get_zobrist_table(shape, num_states + 1)
        self.assertIsNot(table1, table4, "Different arguments should result in a different table object.")

    def test_calculate_board_hash_empty_board(self):
        """
        Tests hashing an empty board.
        """
        shape = (5, 5)
        num_states = 3
        z_table = get_zobrist_table(shape, num_states)

        empty_board = np.zeros(shape, dtype=int)  # Assumes state 0 is empty
        empty_hash = calculate_board_hash(z_table, empty_board)

        # Since all hashes for state 0 are 0, the XOR sum should be 0.
        self.assertEqual(empty_hash, 0, "Hash of an empty board (all state 0) should be 0.")

    def test_calculate_board_hash_changes_with_moves(self):
        """
        Tests that the hash changes when stones are placed or removed.
        """
        shape = (3, 3)
        num_states = 3
        z_table = get_zobrist_table(shape, num_states)

        board = np.zeros(shape, dtype=int)
        hash1 = calculate_board_hash(z_table, board)

        # Place a stone (state 1)
        board[0, 0] = 1
        hash2 = calculate_board_hash(z_table, board)
        self.assertNotEqual(hash1, hash2, "Hash should change after placing a stone.")

        # Place another stone (state 2)
        board[1, 1] = 2
        hash3 = calculate_board_hash(z_table, board)
        self.assertNotEqual(hash2, hash3, "Hash should change after placing another stone.")
        self.assertNotEqual(hash1, hash3)

        # Remove the first stone (back to state 0)
        board[0, 0] = 0
        hash4 = calculate_board_hash(z_table, board)
        self.assertNotEqual(hash3, hash4, "Hash should change after removing a stone.")

        # Hash4 should be different from hash2 (because board[1,1] is still 2)
        # but also different from hash1 (because board[1,1] is 2, not 0)
        # A more specific check:
        # board_intermediate = np.zeros(shape, dtype=int)
        # board_intermediate[1,1] = 2
        # hash_intermediate = calculate_board_hash(z_table, board_intermediate)
        # self.assertEqual(hash4, hash_intermediate)

    def test_calculate_board_hash_consistency(self):
        """
        Tests that the same board configuration always produces the same hash.
        """
        shape = (4, 4)
        num_states = 3
        z_table = get_zobrist_table(shape, num_states)

        board_config = np.array([
            [0, 1, 0, 2],
            [1, 0, 0, 0],
            [0, 2, 1, 0],
            [0, 0, 0, 0]
        ], dtype=int)

        hash_a = calculate_board_hash(z_table, board_config)
        hash_b = calculate_board_hash(z_table, board_config.copy())  # Use a copy

        self.assertEqual(hash_a, hash_b, "Same board configuration should produce the same hash.")

    def test_calculate_board_hash_uniqueness_simple(self):
        """
        Tests that slightly different board configurations produce different hashes.
        (Full collision testing is complex, this is a basic check).
        """
        shape = (2, 2)
        num_states = 2  # 0: empty, 1: piece
        z_table = get_zobrist_table(shape, num_states)

        board1 = np.array([[1, 0], [0, 0]], dtype=int)
        hash1 = calculate_board_hash(z_table, board1)

        board2 = np.array([[0, 1], [0, 0]], dtype=int)
        hash2 = calculate_board_hash(z_table, board2)
        self.assertNotEqual(hash1, hash2, "Different simple configurations should have different hashes.")

        board3 = np.array([[1, 1], [0, 0]], dtype=int)
        hash3 = calculate_board_hash(z_table, board3)
        self.assertNotEqual(hash1, hash3)
        self.assertNotEqual(hash2, hash3)

    def test_zobrist_incremental_update_property(self):
        """
        Tests the incremental update property of Zobrist hashing.
        hash(new_board) = hash(old_board) ^ zobrist(pos, old_piece) ^ zobrist(pos, new_piece)
        """
        shape = (3, 3)
        num_states = 3  # 0: empty, 1: black, 2: white
        z_table = get_zobrist_table(shape, num_states)

        # Initial board (empty)
        board = np.zeros(shape, dtype=int)
        current_hash = calculate_board_hash(z_table, board)
        self.assertEqual(current_hash, 0)  # Empty board hash

        # 1. Place a black stone (state 1) at (0,0)
        pos_to_change = (0, 0)
        old_state_at_pos = board[pos_to_change]  # Should be 0 (empty)
        new_state_at_pos = 1  # Black

        # Calculate expected hash incrementally
        hash_val_old_state = z_table[pos_to_change[0], pos_to_change[1], old_state_at_pos]
        hash_val_new_state = z_table[pos_to_change[0], pos_to_change[1], new_state_at_pos]
        expected_hash_after_move1 = current_hash ^ hash_val_old_state ^ hash_val_new_state

        # Update board and calculate hash directly
        board[pos_to_change] = new_state_at_pos
        actual_hash_after_move1 = calculate_board_hash(z_table, board)

        self.assertEqual(actual_hash_after_move1, expected_hash_after_move1,
                         "Incremental hash update failed for placing first stone.")
        current_hash = actual_hash_after_move1

        # 2. Change the stone at (0,0) from black (1) to white (2)
        old_state_at_pos = board[pos_to_change]  # Should be 1 (black)
        new_state_at_pos = 2  # White

        hash_val_old_state = z_table[pos_to_change[0], pos_to_change[1], old_state_at_pos]
        hash_val_new_state = z_table[pos_to_change[0], pos_to_change[1], new_state_at_pos]
        expected_hash_after_move2 = current_hash ^ hash_val_old_state ^ hash_val_new_state

        board[pos_to_change] = new_state_at_pos
        actual_hash_after_move2 = calculate_board_hash(z_table, board)

        self.assertEqual(actual_hash_after_move2, expected_hash_after_move2,
                         "Incremental hash update failed for changing stone color.")
        current_hash = actual_hash_after_move2

        # 3. Remove the stone at (0,0) (back to empty, state 0)
        old_state_at_pos = board[pos_to_change]  # Should be 2 (white)
        new_state_at_pos = 0  # Empty

        hash_val_old_state = z_table[pos_to_change[0], pos_to_change[1], old_state_at_pos]
        hash_val_new_state = z_table[pos_to_change[0], pos_to_change[1], new_state_at_pos]  # Should be 0
        expected_hash_after_move3 = current_hash ^ hash_val_old_state ^ hash_val_new_state

        board[pos_to_change] = new_state_at_pos
        actual_hash_after_move3 = calculate_board_hash(z_table, board)

        self.assertEqual(actual_hash_after_move3, expected_hash_after_move3,
                         "Incremental hash update failed for removing stone.")
        # The final hash should be 0 as the board is empty again
        self.assertEqual(actual_hash_after_move3, 0, "Board should be empty, hash should be 0.")


if __name__ == '__main__':
    unittest.main()