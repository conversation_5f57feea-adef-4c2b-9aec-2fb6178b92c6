"""
This file contains the GoBoard class, which represents a Go board using a NumPy array.
"""

import enum
from dataclasses import dataclass

import numpy as np

from games.go.hash_utilities import get_zobrist_table


class PositionState(enum.IntEnum):
    WHITE = -1
    EMPTY = 0
    BLACK = 1

    def __str__(self):
        if self == PositionState.EMPTY:
            return ' '
        elif self == PositionState.BLACK:
            return '○'
        elif self == PositionState.WHITE:
            return '●'
        else:
            raise ValueError(f"Invalid position value: {self.value}")

@dataclass(frozen=True)
class Move:
    location: tuple | None # None for pass
    color: int

class GoBoard:
    """
        Represents a Go board using a NumPy array.

        Attributes:
            shape (int, int): The size of the board (e.g., 9, 13, 19).
    """
    def make_move(self, move):
        raise NotImplementedError("make_move must be implemented by subclasses.")

    def score(self):
        board_copy = self.board.copy()

        # Get coordinates of empty spots
        empty_points = np.argwhere(self.empties)
        for point in empty_points:
            point = tuple(point)
            if board_copy[point] != 0:
                continue  # Already part of a territory that was filled

            q = [point]
            visited = {point}
            territory = True
            owner = 0  # 0 for neutral, 1 for black, -1 for white

            while q:
                curr = q.pop(0)

                for neighbor in self._get_neighbors(curr):
                    if neighbor in visited:
                        continue

                    neighbor_val = self.board[neighbor]
                    if neighbor_val == 0:
                        visited.add(neighbor)
                        q.append(neighbor)
                    else:  # It's a colored stone
                        if owner == 0:  # First colored stone found
                            owner = neighbor_val
                        elif owner != neighbor_val:  # Touches both black and white stones
                            territory = False

            if territory and owner != 0:
                for p in visited:
                    board_copy[p] = owner

        return np.sum(board_copy)


    def get_empty_locations(self):
        return self.empties

    def _place_stone(self, move):
        move_location, color = move.location, move.color
        self.board[move_location] = color
        self.empties[move_location] = False
        self.hash ^= get_zobrist_table(self.shape, 3)[move_location][color]

    def _remove_stone(self, location):
        previous_color = self.board[location]
        self.board[location] = 0
        self.empties[location] = True
        self.hash ^= get_zobrist_table(self.shape, 3)[location][previous_color]

    def _get_neighbors(self, location):
        """Returns a list of valid neighboring coordinates (row, col)."""
        row, col = location
        neighbors = []
        if row > 0:
            neighbors.append((row - 1, col))
        if row < self.board.shape[0] - 1:
            neighbors.append((row + 1, col))
        if col > 0:
            neighbors.append((row, col - 1))
        if col < self.board.shape[1] - 1:
            neighbors.append((row, col + 1))
        return neighbors

    def __init__(self, shape):
        self.board = np.zeros(shape, dtype=int)
        self.empties = np.ones(shape, dtype=bool)
        self.shape = shape
        self.hash = 0

    def __str__(self):
        output = []
        for row in self.board:
            output.append("|".join(map(str, [PositionState(val) for val in row])))
        return "\n".join(output)

    def __getitem__(self, item):
        return self.board[item]

    def __hash__(self):
        return int(self.hash)

    def __eq__(self, other):
        return np.array_equal(self.board, other.board)
