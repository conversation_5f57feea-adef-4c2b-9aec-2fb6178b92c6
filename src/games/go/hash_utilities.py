import functools
import time
import numpy as np

@functools.cache
def get_zobrist_table(shape: tuple[int, int], number_of_possible_states: int) -> np.ndarray:
    """
    Generates a Zobrist table for a given board shape and number of states.
    The value for state 0 (e.g., representing an empty cell) is always set to 0.
    Other states receive a random 64-bit integer.
    """
    # Generate random 64-bit integers for each state at each position.
    # We start from 1 to ensure non-zero hashes for actual pieces/states.
    zobrist_table = np.random.randint(
        1,
        np.iinfo(np.uint64).max,
        size=(*shape, number_of_possible_states),
        dtype=np.uint64
    )
    zobrist_table[:, :, 0] = 0
    return zobrist_table


def calculate_board_hash(zobrist_table: np.ndarray, board: np.ndarray) -> np.uint64:
    """
    Calculates the Zobrist hash for a given board configuration.

    Args:
        zobrist_table: The pre-generated Zobrist table.
                       Shape: (board_rows, board_cols, num_states)
        board: The game board. Shape: (board_rows, board_cols).
               Contains integer states that are used to index into the
               last dimension of the zobrist_table.

    Returns:
        A 64-bit unsigned integer representing the board's hash.
    """
    row_indices = np.arange(zobrist_table.shape[0])[:, None]
    col_indices = np.arange(zobrist_table.shape[1])

    selected_hashes = zobrist_table[row_indices, col_indices, board] # select hash for each cell
    return np.bitwise_xor.reduce(selected_hashes.ravel()) # flatten(no-copy) and reduce
