import numpy as np
from scipy.ndimage import convolve


def generate_feature_map(board_list, current_color):
    """
    TODO: Can add a board mask for board size extensibility.
    The first 16 channels alternate between player and opponent stones. Each is just a binary mask.
    The channel is the number of liberties for each location.
    """
    board_shape = board_list[0].shape
    features = np.zeros((16, *board_shape), dtype=np.int8)
    # The first 14 feature maps are the last 7 moves.
    # The even feature maps(index: 0, 2, 4, 6, 8, 10, 12) are the player's moves
    # The odd feature maps(index: 1, 3, 5, 7, 9, 11, 13) are the opponent's moves
    for i, board in enumerate(board_list[:-8:-1]):
        features[2*i,:,:] = board[:,:] == current_color
        features[2*i+1,:,:] = board[:,:] == -current_color
    unfilled_locations = 1 - (features[0,:,:] + features[1,:,:])
    kernel = np.array([[0, 1, 0], [1, 0, 1], [0, 1, 0]])
    liberties = convolve(unfilled_locations, kernel, mode="constant")
    # The 15th feature map is the number of liberties for each location
    features[14,:,:] = liberties
    # The 16th feature map is the difference between the two latest boards.
    if len(board_list) >= 2:
        features[15,:,:] = board_list[-1] - board_list[-2]
    return features

if __name__ == '__main__':
    # Test generate_feature_map
    boards = [np.array([
        [1,0,-1,1],
        [1,0,-1,0],
        [1,0,-1,1],
        [1,0,-1,1],
    ]), np.array([
        [1,1,-1,1],
        [1,1,-1,0],
        [1,0,-1,1],
        [1,1,-1,1],
    ])]
    features = generate_feature_map_big(boards, 1)
    print(features.shape, features.dtype)
