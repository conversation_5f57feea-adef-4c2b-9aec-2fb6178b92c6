import torch
import torch.nn as nn


class ResidualBlock(nn.Module):
    """A residual block. Wraps a module and adds the residual connection."""
    def __init__(self, nested_module):
        super().__init__()
        self.nested_module = nested_module

    def forward(self, x):
        return x + self.nested_module(x)


class NormActConv(nn.Sequential):
    """"""
    def __init__(self, in_channels, out_channels, kernel_size):
        self.in_channels = in_channels

        super().__init__(
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, out_channels, kernel_size, padding="same")
        )

    def forward(self, x):
        return super().forward(x)


class NestedBottleneckResidualBlock(nn.Module):
    def __init__(self, channels=20, channel_downscale=2):
        super().__init__()
        inner_channels = channels // channel_downscale
        self.module = ResidualBlock(
            nn.Sequential(
                NormActConv(channels, inner_channels, 1),
                ResidualBlock(
                    nn.Sequential(
                        NormActConv(inner_channels, inner_channels, 3),
                        NormActConv(inner_channels, inner_channels, 3)
                    )
                ),
                ResidualBlock(
                    nn.Sequential(
                        NormActConv(inner_channels, inner_channels, 3),
                        NormActConv(inner_channels, inner_channels, 3)
                    )
                ),
                NormActConv(inner_channels, channels, 1)
            )
        )

    def forward(self, x):
        return self.module(x)


class PolicyHead(nn.Module):
    def __init__(self, board_shape=(19, 19), num_channels=2):
        super().__init__()
        # Add in global pooling.
        self.module = nn.Sequential(
            nn.Flatten(start_dim=1),
            nn.Linear(board_shape[0] * board_shape[1] * num_channels, board_shape[0] * board_shape[1]),
        )

    def forward(self, x):
        return self.module(x)


class ValueHead(nn.Module):
    def __init__(self, board_shape=(19, 19), num_channels=1):
        super().__init__()
        self.module = nn.Sequential(
            nn.Flatten(start_dim=1),
            nn.Linear(board_shape[0] * board_shape[1] * num_channels, 1),
            nn.Tanh()
        )

    def forward(self, x):
        return self.module(x)


class ResNet(nn.Module):
    def __init__(self, num_blocks=5, board_rep_channels=20, trunk_channels=6, board_shape=(19, 19), num_policy_channels=2, num_value_channels=3):
        super().__init__()
        self.module = nn.Sequential(
            nn.Conv2d(board_rep_channels, trunk_channels, kernel_size=3, padding="same"),
            nn.Sequential(*[NestedBottleneckResidualBlock(channels=trunk_channels) for _ in range(num_blocks)]),
            NormActConv(trunk_channels, num_policy_channels + num_value_channels, 1),
        )

        self.num_policy_channels = num_policy_channels
        self.num_value_channels = num_value_channels

        self.policy_head = PolicyHead(board_shape, num_policy_channels)
        self.value_head = ValueHead(board_shape, 3)

    def forward(self, x):
        post_trunk = self.module(x)
        return (self.policy_head(post_trunk[:,0:self.num_policy_channels,:,:]),
                self.value_head(post_trunk[:,self.num_policy_channels:,:,:]))


if __name__ == '__main__':
    resnet = ResNet(2, 20, 12).cuda()
    print(resnet)
    print(resnet(torch.rand(1, 20, 19, 19).cuda())[0].shape)
