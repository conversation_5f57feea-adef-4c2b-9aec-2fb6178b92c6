import math

class Node:
    def __init__(self, game_state, parent=None, action_that_led_here=None):
        self.game_state = game_state  # The actual state of the game
        self.parent = parent
        self.action_that_led_here = action_that_led_here  # Action taken by parent

        self.N = 0  # Visit count for this node (as per pseudocode's update)
        self.Q = 0.0  # Mean action value (as per pseudocode's Q definition)
        self.U = 0.0  # Utility: game outcome if terminal, or NN eval if new leaf.

        # Children: maps action -> (child_node, edge_visits_for_that_action)
        self.children_and_edge_visits: dict[int, tuple['Node', int]] = {}

        # Priors for actions from this state (P(s,a)) - populated from NN/heuristic
        self.action_priors: dict[int, float] = {}

    @property
    def game_state_hash(self):
        # Assumes game_state has a .hash property or is hashable itself
        if hasattr(self.game_state, 'hash'):
            return self.game_state.hash
        return hash(self.game_state) # Fallback to direct hash

    def __repr__(self):
        return (f"Node(state={self.game_state}, N={self.N}, Q={self.Q:.3f}, U={self.U:.3f}, "
                f"Priors={ {k: round(v, 2) for k, v in self.action_priors.items()} })")


def is_game_over(node: Node) -> bool:
    return node.game_state.is_terminal


def get_utility_of_game_outcome(game_state: ToyGameState) -> float:
    if not game_state.is_terminal:
        raise ValueError("get_utility_of_game_outcome called on non-terminal state")
    return game_state.terminal_value


def get_utility_from_neural_net(game_state: ToyGameState) -> float:
    # Mock NN evaluation; in a real system, this would query the neural network
    return nn_evals_heuristic.get(game_state.state_id, 0.0)


C_PUCT = 1.0  # Exploration constant for PUCT


def select_action_according_to_puct(node: Node) -> any:
    best_action = None
    best_puct_score = -float('inf')

    # N(s) in the PUCT formula: sum of edge visits of children from current node s
    sum_edge_visits_of_children = sum(visits for _, visits in node.children_and_edge_visits.values())

    if not node.action_priors:
        # This might indicate an issue if called on a node that hasn't had priors set.
        # For the toy example, we ensure priors are set.
        print(f"Warning: select_action_according_to_puct called on node {node.game_state} without action_priors.")
        if node.game_state.children_actions:  # Fallback to first game-defined action
            return next(iter(node.game_state.children_actions.keys()))
        return None

    for action, prior_p in node.action_priors.items():
        if action in node.children_and_edge_visits:
            child_node, N_s_a = node.children_and_edge_visits[action]  # N(s,a)
            q_s_a = child_node.Q  # Q(s,a) is the Q value of the child state
        else:
            N_s_a = 0
            q_s_a = 0.0  # Default Q for unvisited actions

        # PUCT formula: Q(s,a) + C_puct * P(s,a) * sqrt(sum_b N(s,b)) / (1 + N(s,a))
        exploration_numerator = math.sqrt(sum_edge_visits_of_children)
        exploration_term = C_PUCT * prior_p * (exploration_numerator / (1 + N_s_a))

        current_puct_score = q_s_a + exploration_term

        if current_puct_score > best_puct_score:
            best_puct_score = current_puct_score
            best_action = action

    if best_action is None:
        # This could happen if all priors are 0 or action_priors is empty.
        # Fallback to selecting the first available action if priors were problematic.
        if node.action_priors:
            best_action = next(iter(node.action_priors.keys()))
        elif node.game_state.children_actions:  # If priors were empty but game has actions
            best_action = next(iter(node.game_state.children_actions.keys()))

    if best_action is None and not node.game_state.is_terminal:
        # This indicates a non-terminal state with no selectable actions, an issue.
        raise Exception(f"Node {node.game_state} is not terminal but has no actions to select.")

    return best_action


# --- Global store for nodes (transposition table) ---
nodes_by_hash: dict[any, Node] = {}


# --- Playout Function (Python implementation of the pseudocode) ---
def perform_one_playout(node: Node):
    if is_game_over(node):
        node.U = get_utility_of_game_outcome(node.game_state)
    elif node.N == 0:  # New node not yet visited (or first visit in this search path)
        node.U = get_utility_from_neural_net(node.game_state)
        # Populate action_priors if this is the first time evaluating this node
        if not node.action_priors:  # Check if already populated (e.g. by parent)
            node.action_priors = action_priors_map_heuristic.get(node.game_state.state_id, {})
            # Fallback to uniform priors if not in heuristic map but actions exist
            if not node.action_priors and node.game_state.children_actions:
                num_possible_actions = len(node.game_state.children_actions)
                if num_possible_actions > 0:
                    node.action_priors = {
                        act: 1.0 / num_possible_actions
                        for act in node.game_state.children_actions
                    }
    else:  # Node has been visited before (N > 0 implies it was previously a leaf or an internal node)
        action = select_action_according_to_puct(node)

        # select_action_according_to_puct should raise an error if action is None for non-terminal.

        if action not in node.children_and_edge_visits:
            new_game_state = node.game_state.play(action)

            # Check if this resulting game state has already been seen and has a node
            if new_game_state.hash in nodes_by_hash:
                child_node = nodes_by_hash[new_game_state.hash]
            else:
                # Create new node. Its N, Q, U will be set by the recursive call.
                child_node = Node(game_state=new_game_state, parent=node, action_that_led_here=action)
                nodes_by_hash[new_game_state.hash] = child_node

            node.children_and_edge_visits[action] = (child_node, 0)

        child_node, edge_visits_for_action = node.children_and_edge_visits[action]
        perform_one_playout(child_node)
        node.children_and_edge_visits[action] = (child_node, edge_visits_for_action + 1)

    # Updates for N and Q happen *after* recursion returns or leaf is processed
    current_children_and_edge_visits_list = list(node.children_and_edge_visits.values())

    # Update N for the current node: N(s) = 1 (current path) + sum_b N(s,b)
    node.N = 1 + sum(visits for _, visits in current_children_and_edge_visits_list)

    # Update Q for the current node: Q(s) = (1/N(s)) * [ U(s) + sum_b (N(s,b) * Q(s')) ]
    # where s' is child from action b, and Q(s') is its mean action value.
    sum_child_Q_times_visits = sum(child.Q * visits for child, visits in current_children_and_edge_visits_list)

    # node.N is guaranteed to be at least 1 due to the update rule above.
    node.Q = (1 / node.N) * (node.U + sum_child_Q_times_visits)

    return  # No explicit return value, updates node in-place
