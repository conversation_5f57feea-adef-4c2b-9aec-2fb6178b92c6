[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "GoExperiments"
version = "0.1.0"
description = "Experiments with Go AI."
readme = "README.md" # Optional: if you have a README file
requires-python = ">=3.8" # Specify your minimum Python version
license = {text = "MIT"} # Or any other license you prefer

dependencies = [
    "numpy",
    "scipy",
    "torch"
]
